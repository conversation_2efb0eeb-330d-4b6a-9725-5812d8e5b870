import type { AvroSchema } from './types';
export declare class AvroSchemaValidator {
    private namedSchemas;
    validateSchema(schema: AvroSchema): boolean;
    validateValue(value: unknown, schema: AvroSchema): boolean;
    private validateSchemaInternal;
    private validateStringSchema;
    private validateUnionSchema;
    private validateNullSchema;
    private validateBooleanSchema;
    private validateIntSchema;
    private validateLongSchema;
    private validateFloatSchema;
    private validateDoubleSchema;
    private validateBytesSchema;
    private validateStringTypeSchema;
    private validateRecordSchema;
    private validateR<PERSON>ordField;
    private validateEnumSchema;
    private validateArraySchema;
    private validateMapSchema;
    private validateFixedSchema;
    private validateValueAgainstSchema;
    private validateValueAgainstStringSchema;
    private validateValueAgainstRecord;
    private validateValueAgainstEnum;
    private validateValueAgainstArray;
    private validateValueAgainstMap;
    private validateValueAgainstFixed;
    private getSchemaTypeName;
    private getFullName;
}
