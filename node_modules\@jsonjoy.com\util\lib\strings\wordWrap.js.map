{"version": 3, "file": "wordWrap.js", "sourceRoot": "", "sources": ["../../src/strings/wordWrap.ts"], "names": [], "mappings": ";;;AAIA,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE,CAC/B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3F,MAAM,UAAU,GAAG,CAAC,GAAa,EAAE,IAAY,EAAE,EAAE;IACjD,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9B,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEK,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,UAAuB,EAAE,EAAY,EAAE;IAC3E,IAAI,CAAC,GAAG;QAAE,OAAO,EAAE,CAAC;IAEpB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;IAClC,MAAM,WAAW,GAAG,MAAM,GAAG,KAAK,GAAG,kDAAkD,CAAC;IACxF,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACxC,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,EAAc,CAAC,CAAC;IAEpF,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AATW,QAAA,QAAQ,YASnB"}