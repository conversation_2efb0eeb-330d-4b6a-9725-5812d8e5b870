import type { CoreFsaContext } from './types';
/**
 * Creates a new {@link CoreFsaContext}.
 */
export declare const ctx: (partial?: Partial<CoreFsaContext>) => CoreFsaContext;
export declare const basename: (path: string, separator: string) => string;
export declare const assertName: (name: string, method: string, klass: string) => void;
export declare const assertCanWrite: (mode: "read" | "readwrite") => void;
export declare const newNotFoundError: () => DOMException;
export declare const newTypeMismatchError: () => DOMException;
export declare const newNotAllowedError: () => DOMException;
