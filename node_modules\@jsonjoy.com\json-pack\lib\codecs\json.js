"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonJsonValueCodec = void 0;
const JsonEncoder_1 = require("../json/JsonEncoder");
const JsonDecoder_1 = require("../json/JsonDecoder");
class JsonJsonValueCodec {
    constructor(writer) {
        this.id = 'json';
        this.format = 2;
        this.encoder = new JsonEncoder_1.<PERSON><PERSON><PERSON>nco<PERSON>(writer);
        this.decoder = new JsonDecoder_1.JsonDecoder();
    }
}
exports.JsonJsonValueCodec = JsonJsonValueCodec;
//# sourceMappingURL=json.js.map