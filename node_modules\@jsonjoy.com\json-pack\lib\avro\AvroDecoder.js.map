{"version": 3, "file": "AvroDecoder.js", "sourceRoot": "", "sources": ["../../src/avro/AvroDecoder.ts"], "names": [], "mappings": ";;;AAAA,4DAAuD;AAQvD,MAAa,WAAW;IAAxB;QACS,WAAM,GAAG,IAAI,eAAM,EAAE,CAAC;IA8O/B,CAAC;IA5OQ,IAAI,CAAC,KAAiB;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,MAAM,CAAC,KAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAKM,OAAO;QACZ,MAAM,IAAI,KAAK,CAAC,kFAAkF,CAAC,CAAC;IACtG,CAAC;IAKM,QAAQ;QAEb,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,WAAW;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IAKM,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAKM,QAAQ;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAG5C,IAAI,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC7F,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAKM,SAAS;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACrD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,UAAU;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACrD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,SAAS;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAKM,UAAU;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAMM,SAAS,CAAI,UAAmB;QACrC,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxC,IAAI,KAAK,KAAK,CAAC;gBAAE,MAAM;YAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAMM,OAAO,CAAI,WAAoB;QACpC,MAAM,MAAM,GAAsB,EAAE,CAAC;QAErC,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxC,IAAI,KAAK,KAAK,CAAC;gBAAE,MAAM;YAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC9B,IAAI,GAAG,KAAK,WAAW;oBAAE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;gBACxD,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAMM,SAAS,CAAI,aAA6B;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC7D,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACrC,OAAO,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC;IACxB,CAAC;IAMM,QAAQ;QACb,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;IACxD,CAAC;IAKM,SAAS,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAMM,UAAU,CAAI,YAA8B;QACjD,MAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;YAGrC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;QACnC,CAAC;QACD,OAAO,MAAW,CAAC;IACrB,CAAC;IAOO,kBAAkB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC;YAEjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;gBAAE,MAAM;YAE/B,KAAK,IAAI,CAAC,CAAC;YACX,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;gBAChB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAED,OAAO,MAAM,KAAK,CAAC,CAAC;IACtB,CAAC;IAKO,WAAW;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEtB,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACjC,MAAM,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;YAEzC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC;gBAAE,MAAM;YAE/C,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,cAAc,CAAC,KAAa;QAClC,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACtC,CAAC;IAKO,cAAc,CAAC,KAAa;QAClC,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;CACF;AA/OD,kCA+OC"}