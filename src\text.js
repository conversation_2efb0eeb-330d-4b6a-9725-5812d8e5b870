const path = require('path')
let src = path.join(__dirname, 'node', 'node.js', '123')
let ext = path.extname(path.join(__dirname, 'node', 'node.js', '123'))
let obj = path.parse(src)
console.log(src)
console.log(ext)
console.log(obj)

const dns = require('dns')
let name = 'bilbil.com';
let ip;
dns.resolve(name, function (err, address) {
  if (err) {
    console.log(err);
  }
  console.log(address);
})
dns.lookup(name, function (err, address) {
  if (err) {
    console.log(err);
  }
  console.log(address);
  ip = address
})
console.log(ip);

dns.reverse("***************", function (err, domain) {
  if (err) {
    console.log(err);
  }
  console.log(domain);
})