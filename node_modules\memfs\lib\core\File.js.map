{"version": 3, "file": "File.js", "sourceRoot": "", "sources": ["../../src/core/File.ts"], "names": [], "mappings": ";;;AAAA,4CAAyC;AAKzC,MAAM,EAAE,QAAQ,EAAE,GAAG,qBAAS,CAAC;AAE/B;;;;GAIG;AACH,MAAa,IAAI;IAOf;;;;;;;OAOG;IACH,YACkB,IAAU,EACV,IAAU,EACnB,KAAa,EACb,EAAU;QAHD,SAAI,GAAJ,IAAI,CAAM;QACV,SAAI,GAAJ,IAAI,CAAM;QACnB,UAAK,GAAL,KAAK,CAAQ;QACb,OAAE,GAAF,EAAE,CAAQ;QAEjB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ;YAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5D,CAAC;IAED,SAAS,CAAC,QAAQ,GAAG,MAAM;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,QAAQ,CAAC,GAAY;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,QAAgB;QACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,GAAW,EAAE,SAAiB,CAAC,EAAE,SAAiB,GAAG,CAAC,MAAM,EAAE,QAAwB;QAC1F,IAAI,OAAO,QAAQ,KAAK,QAAQ;YAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CACF,GAAwC,EACxC,SAAiB,CAAC,EAClB,SAAiB,GAAG,CAAC,UAAU,EAC/B,QAAiB;QAEjB,IAAI,OAAO,QAAQ,KAAK,QAAQ;YAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,IAAY;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,GAAW,EAAE,GAAW;QAC5B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;CACF;AA/ED,oBA+EC"}