# @jsonjoy.com/codegen

A no-dependencies, low-level, high-performance JIT code generation package for JavaScript.

## Features

- **Zero dependencies** - Lightweight and fast to install
- **High performance** - Generates optimized JavaScript functions at runtime
- **Type safe** - Full TypeScript support with comprehensive type definitions
- **Flexible** - Supports various code generation patterns and techniques
- **Production ready** - Battle-tested and optimized for real-world usage

## Use Cases

- Deep equality comparison functions with known schemas
- JSON Patch execution with pre-known patches
- Optimized validation and serialization functions
- Custom function generation based on runtime data
- Performance-critical code that benefits from JIT compilation

## Installation

```bash
npm install @jsonjoy.com/codegen
```

## Quick Start

```typescript
import { Codegen } from '@jsonjoy.com/codegen';

const codegen = new Codegen();
// Add your code generation logic here
const optimizedFunction = codegen.compile();
```

## License

Apache-2.0