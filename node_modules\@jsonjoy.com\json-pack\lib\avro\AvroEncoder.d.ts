import type { IWriter, IWriterGrowable } from '@jsonjoy.com/buffers/lib';
import type { BinaryJsonEncoder } from '../types';
export declare class AvroEncoder implements BinaryJsonEncoder {
    readonly writer: IWriter & IWriterGrowable;
    constructor(writer: IWriter & IWriterGrowable);
    encode(value: unknown): Uint8Array;
    writeUnknown(value: unknown): void;
    writeAny(value: unknown): void;
    writeNull(): void;
    writeBoolean(bool: boolean): void;
    writeInt(int: number): void;
    writeLong(long: number | bigint): void;
    writeFloatAvro(float: number): void;
    writeDouble(double: number): void;
    writeBin(bytes: Uint8Array): void;
    writeStr(str: string): void;
    writeArr(arr: unknown[]): void;
    writeObj(obj: Record<string, unknown>): void;
    writeNumber(num: number): void;
    writeInteger(int: number): void;
    writeUInteger(uint: number): void;
    writeFloat(float: number): void;
    private writeFloatValue;
    writeAsciiStr(str: string): void;
    private writeVarIntSigned;
    private writeVarIntUnsigned;
    private writeVarLong;
    private encodeZigZag32;
    private encodeZigZag64;
}
