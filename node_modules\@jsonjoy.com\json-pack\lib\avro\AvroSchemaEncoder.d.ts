import type { IWriter, IWriterGrowable } from '@jsonjoy.com/buffers/lib';
import type { AvroSchema, AvroRecordSchema, AvroEnumSchema, AvroArraySchema, AvroMapSchema, AvroUnionSchema, AvroFixedSchema, AvroNullSchema } from './types';
export declare class AvroSchemaEncoder {
    readonly writer: IWriter & IWriterGrowable;
    private encoder;
    private validator;
    private namedSchemas;
    constructor(writer: IWriter & IWriterGrowable);
    encode(value: unknown, schema: AvroSchema, selectedIndex?: number): Uint8Array;
    writeNull(schema: AvroNullSchema | AvroSchema): void;
    writeBoolean(value: boolean, schema: AvroSchema): void;
    writeInt(value: number, schema: AvroSchema): void;
    writeLong(value: number | bigint, schema: AvroSchema): void;
    writeFloat(value: number, schema: AvroSchema): void;
    writeDouble(value: number, schema: AvroSchema): void;
    writeBytes(value: Uint8Array, schema: AvroSchema): void;
    writeString(value: string, schema: AvroSchema): void;
    writeRecord(value: Record<string, unknown>, schema: AvroRecordSchema): void;
    writeEnum(value: string, schema: AvroEnumSchema): void;
    writeArray(value: unknown[], schema: AvroArraySchema): void;
    writeMap(value: Record<string, unknown>, schema: AvroMapSchema): void;
    writeUnion(value: unknown, schema: AvroUnionSchema, selectedIndex?: number): void;
    writeFixed(value: Uint8Array, schema: AvroFixedSchema): void;
    writeNumber(value: number, schema: AvroSchema): void;
    private writeValue;
    private validateSchemaType;
    private resolveSchema;
    private collectNamedSchemas;
    private getFullName;
    private writeVarIntUnsigned;
    private writeVarIntSigned;
    private encodeZigZag32;
}
