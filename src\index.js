const http = require('http')
const fs = require('fs')
const url = require('url')
const server = http.createServer(function (req, res) {
  let pathname = url.parse(req.url).pathname;


  if (pathname == '/add.html') { 
  res.writeHead(200, {
    'content-type': 'text/html'
  })
  let data = fs.readFileSync('./src/zzz.html')
  res.write(data)
  res.end()
}
if (pathname == '.user/add') {
  res.writeHead(200, {
    "content-type": 'text/html'
  })
  res.write("user/add")
  res.end()
}
if (pathname === './' || pathname == '/zzz.html') {
  res.writeHead(200, {
    "content-type" : 'text/html'
  })
  let data = fs.readFileSync('./src/zzz.html')
  res.write(data)
  res.end()
}
})

// let reqDate = ""
// http.request({
//   host:"127.0.0.1",
//   port:3000,
//   method:'get',
// },function(res){
//   res.on("data",function(chunk){
//     reqDate += chunk
//   })
//   res.on("end",function(){
//     console.log(reqDate)
//   })
// }).end()
// let option = {
//   host:"127.0.0.1",
//   port:3000,
//   method:'get',
// }
// const req = http.request(option)
// req.on("response",function(res){
//   res.on("data",function(chunk){
//     reqDate += chunk
//   })
//     res.on("end",function(){
//     console.log(reqDate)
//   })
// }).end()

server.listen(3000, function () {
  console.log("listening port 3000")
})

// const url = require('url')
// let parseUrl = "https://syzs.qq.com/pcsem/game/slg/com.tencent.KiHan?supply_id=**********&ocpc=0&landing_type=syzs&account_id=********&plan_id=*********&group_id=**********&keyword_id=************&creative_id=***********&adposition=cl2&pagenum=1&matchtype=2&bd_vid=10434738526498272707"
// console.log(url.parse(parseUrl))

// const queryString = require('querystring')
// // console.log(queryString.parse("tn=baiduimage&ps=1"))
// let s ="tn=baiduimage&ps=1"
// let result = queryString.parse(s)
// console.log(result)
// console.log(queryString.stringify(result));

// const util = require('util')
// let obj = {
//   tn: 'baiduimage',
//   ps:'1'
// }
// console.log(util.inspect(obj));
