# util

Useful utilities for TypeScript programming.

This package includes the following utilities:

- `json-random`: Generate random JSON objects.
- `json-equal`: Very fast JSON deep equality comparison.
- `json-brand`: TypeScript branded type for JSON strings.
- `codegen`: JIT JavaScript code generation utilities.
- `buffers`: Various helper functions to work with `Uint8Array` and `Buffer` blobs.
- `strings`: Various helper functions to work with UTF-8 strings.
- `sort`: Fast sorting for small arrays.
