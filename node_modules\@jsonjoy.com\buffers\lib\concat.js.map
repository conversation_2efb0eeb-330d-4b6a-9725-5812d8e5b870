{"version": 3, "file": "concat.js", "sourceRoot": "", "sources": ["../src/concat.ts"], "names": [], "mappings": ";;;AAAO,MAAM,MAAM,GAAG,CAAC,CAAa,EAAE,CAAa,EAAc,EAAE;IACjE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IAChD,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACX,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;IACrB,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AALW,QAAA,MAAM,UAKjB;AAEK,MAAM,UAAU,GAAG,CAAC,IAAkB,EAAc,EAAE;IAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,IAAI,GAAG,CAAC,EACV,MAAM,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACxD,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;IACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACtB,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;IACxB,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAZW,QAAA,UAAU,cAYrB;AAEK,MAAM,WAAW,GAAG,CAAC,IAAkB,EAAc,EAAE;IAC5D,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;QACpB,KAAK,CAAC;YACJ,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAC3B,KAAK,CAAC;YACJ,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;QACjB;YACE,OAAO,IAAA,kBAAU,EAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC,CAAC;AATW,QAAA,WAAW,eAStB"}