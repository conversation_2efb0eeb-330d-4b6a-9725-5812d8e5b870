{"version": 3, "file": "AvroEncoder.js", "sourceRoot": "", "sources": ["../../src/avro/AvroEncoder.ts"], "names": [], "mappings": ";;;AAQA,MAAa,WAAW;IACtB,YAA4B,MAAiC;QAAjC,WAAM,GAAN,MAAM,CAA2B;IAAG,CAAC;IAE1D,MAAM,CAAC,KAAc;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAKM,YAAY,CAAC,KAAc;QAChC,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEM,QAAQ,CAAC,KAAc;QAC5B,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,MAAM;wBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAgC,CAAC,CAAC;oBACzD,KAAK,KAAK;wBACR,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAkB,CAAC,CAAC;oBAC3C,KAAK,UAAU;wBACb,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAmB,CAAC,CAAC;oBAC5C;wBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;YACD,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/B,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1B;gBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAKM,SAAS;IAEhB,CAAC;IAKM,YAAY,CAAC,IAAa;QAC/B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAKM,QAAQ,CAAC,GAAW;QACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IAKM,SAAS,CAAC,IAAqB;QACpC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAKM,cAAc,CAAC,KAAa;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IAKM,WAAW,CAAC,MAAc;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IAKM,QAAQ,CAAC,KAAiB;QAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAKM,QAAQ,CAAC,GAAW;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;QAGnC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAGd,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;QAGxB,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC;QACxB,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,gBAAgB,GAAG,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC;QAGjD,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,YAAY,GAAG,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC3D,MAAM,CAAC,CAAC,GAAG,YAAY,GAAG,gBAAgB,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;QACpB,CAAC;IACH,CAAC;IAKM,QAAQ,CAAC,GAAc;QAC5B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAKM,QAAQ,CAAC,GAA4B;QAC1C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAOM,WAAW,CAAC,GAAW;QAC5B,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,GAAG,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;gBAC5C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAKM,YAAY,CAAC,GAAW;QAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAKM,aAAa,CAAC,IAAY;QAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAKM,UAAU,CAAC,KAAa;QAC7B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAKO,eAAe,CAAC,KAAa;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IAKM,aAAa,CAAC,GAAW;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAOO,iBAAiB,CAAC,KAAa;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC;QACpB,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAC7B,CAAC,MAAM,CAAC,CAAC;QACX,CAAC;QACD,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC;IAKO,mBAAmB,CAAC,KAAa;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC;QACpB,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAC7B,CAAC,MAAM,CAAC,CAAC;QACX,CAAC;QACD,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC;IAKO,YAAY,CAAC,KAAa;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,GAAG,KAAK,CAAC;QACd,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAExB,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,KAAK,KAAK,CAAC;QACd,CAAC;QACD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAC9B,CAAC;IAKO,cAAc,CAAC,KAAa;QAClC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;IACtC,CAAC;IAKO,cAAc,CAAC,KAAa;QAClC,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,CAAC;CACF;AAzRD,kCAyRC"}