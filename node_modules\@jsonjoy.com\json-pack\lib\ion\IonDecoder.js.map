{"version": 3, "file": "IonDecoder.js", "sourceRoot": "", "sources": ["../../src/ion/IonDecoder.ts"], "names": [], "mappings": ";;;AACA,qDAAgD;AAChD,qCAAgC;AAChC,uCAA6C;AAG7C,MAAa,UAAgF,SAAQ,+BAAiB;IACpH,YAAY,MAAU;QACpB,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;IAEM,MAAM,CAAC,IAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAGxB,IAAI,CAAC,OAAO,GAAG,IAAI,eAAM,CAAC,4BAAkB,EAAE,EAAE,CAAC,CAAC;QAGlD,IAAI,CAAC,WAAW,EAAE,CAAC;QAGnB,IAAI,CAAC,eAAe,EAAE,CAAC;QAGvB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;IAEM,IAAI;QACT,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;CACF;AAxBD,gCAwBC"}