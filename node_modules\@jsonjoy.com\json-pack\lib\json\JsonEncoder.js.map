{"version": 3, "file": "JsonEncoder.js", "sourceRoot": "", "sources": ["../../src/json/JsonEncoder.ts"], "names": [], "mappings": ";;;AAAA,qEAAgE;AAIhE,MAAa,WAAW;IACtB,YAA4B,MAAiC;QAAjC,WAAM,GAAN,MAAM,CAA2B;IAAG,CAAC;IAE1D,MAAM,CAAC,KAAc;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAOM,YAAY,CAAC,KAAc;QAChC,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEM,QAAQ,CAAC,KAAc;QAC5B,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;YAC3C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,MAAM;wBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAgC,CAAC,CAAC;oBACzD,KAAK,KAAK;wBACR,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAkB,CAAC,CAAC;oBAC3C,KAAK,UAAU;wBACb,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAmB,CAAC,CAAC;oBAC5C;wBACE,IAAI,KAAK,YAAY,UAAU;4BAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBAC7D,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;4BAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBACtD,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3B,CAAC;YACD;gBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAEM,UAAU;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE9B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAO,CAAC,CAAC;QAC3B,CAAC,IAAI,CAAC,CAAC;QACP,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAEM,YAAY,CAAC,IAAa;QAC/B,IAAI,IAAI;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;IAEM,WAAW,CAAC,GAAW;QAC5B,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAEM,YAAY,CAAC,GAAW;QAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEM,aAAa,CAAC,IAAY;QAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAEM,QAAQ,CAAC,GAAe;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;QAE9C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAa,CAAC,CAAC;QACjC,CAAC,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAO,CAAC,CAAC;QAC3B,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,GAAG,IAAA,yBAAW,EAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAEM,QAAQ,CAAC,GAAW;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;YACjB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YAC3B,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAChC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC/B,QAAQ,IAAI,EAAE,CAAC;oBACb,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE;wBACL,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;wBAClB,MAAM;gBACV,CAAC;gBACD,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;oBACjC,OAAO;gBACT,CAAC;;oBAAM,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YAC3B,CAAC;YACD,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YAClB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO;QACT,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAEM,aAAa,CAAC,GAAW;QAC9B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE;oBACL,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;oBAClB,MAAM;YACV,CAAC;YACD,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QACpB,CAAC;QACD,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAClB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAEM,QAAQ,CAAC,GAAc;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC;QACD,IAAI,IAAI,IAAI,CAAC;YAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,iBAAiB;QACtB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,QAAQ,CAAC,GAA4B;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM;YAAE,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC;QACD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IACpC,CAAC;IAEM,iBAAiB;QACtB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,oBAAoB;QACzB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAIM,aAAa;QAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa,CAAC,GAAW;QAC9B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa;QAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa,CAAC,GAAe;QAClC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,aAAa,CAAC,IAAa;QAChC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,aAAa;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,aAAa,CAAC,GAAW,EAAE,KAAc;QAC9C,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;CACF;AAvRD,kCAuRC"}