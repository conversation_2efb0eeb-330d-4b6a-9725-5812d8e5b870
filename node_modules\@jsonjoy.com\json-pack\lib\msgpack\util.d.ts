import { MsgPackEncoderFast } from './MsgPackEncoderFast';
import { MsgPackEncoder } from './MsgPackEncoder';
import { MsgPackDecoderFast } from './MsgPackDecoderFast';
import { MsgPack } from './types';
export declare const encoder: MsgPackEncoderFast<import("@jsonjoy.com/buffers").IWriter & import("@jsonjoy.com/buffers").IWriterGrowable>;
export declare const encoderFull: MsgPackEncoder<import("@jsonjoy.com/buffers").IWriter & import("@jsonjoy.com/buffers").IWriterGrowable>;
export declare const decoder: MsgPackDecoderFast<import("@jsonjoy.com/buffers/lib/Reader").Reader>;
export declare const encode: <T>(data: T) => MsgPack<T>;
export declare const encodeFull: <T>(data: T) => MsgPack<T>;
export declare const decode: <T>(blob: MsgPack<T>) => T;
export type { MsgPack };
