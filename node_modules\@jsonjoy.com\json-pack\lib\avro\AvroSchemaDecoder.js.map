{"version": 3, "file": "AvroSchemaDecoder.js", "sourceRoot": "", "sources": ["../../src/avro/AvroSchemaDecoder.ts"], "names": [], "mappings": ";;;AAAA,4DAAuD;AACvD,+CAA0C;AAC1C,+DAA0D;AAiB1D,MAAa,iBAAiB;IAK5B,YAA4B,SAAiB,IAAI,eAAM,EAAE;QAA7B,WAAM,GAAN,MAAM,CAAuB;QAFjD,iBAAY,GAAG,IAAI,GAAG,EAA2B,CAAC;QAGxD,IAAI,CAAC,OAAO,GAAG,IAAI,yBAAW,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,yCAAmB,EAAE,CAAC;IAC7C,CAAC;IAKM,MAAM,CAAC,IAAgB,EAAE,MAAkB;QAChD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAG1B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAKO,SAAS,CAAC,MAAkB;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAElD,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;YACvC,QAAQ,cAAc,EAAE,CAAC;gBACvB,KAAK,MAAM;oBACT,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACjC,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBACpC,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBAChC,KAAK,MAAM;oBACT,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACjC,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBAClC,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBACnC,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBAClC,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBACnC;oBACE,MAAM,IAAI,KAAK,CAAC,2BAA2B,cAAc,EAAE,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QACxC,CAAC;QAED,QAAQ,cAAc,CAAC,IAAI,EAAE,CAAC;YAC5B,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YACzC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACvC,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YACxC,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACtC,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YACxC;gBACE,MAAM,IAAI,KAAK,CAAC,wBAAyB,cAAsB,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAKO,UAAU,CAAC,MAAwB;QACzC,MAAM,MAAM,GAA4B,EAAE,CAAC;QAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,CAAC,IAAI,MAAO,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,QAAQ,CAAC,MAAsB;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAEtC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,sBAAsB,KAAK,kBAAkB,MAAM,CAAC,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC;QAChG,CAAC;QAED,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAKO,SAAS,CAAC,MAAuB;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACpE,CAAC;IAKO,OAAO,CAAC,MAAqB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACnE,CAAC;IAKO,SAAS,CAAC,MAAuB;QACvC,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;QACjF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACrD,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;IAKO,SAAS,CAAC,MAAuB;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAKM,QAAQ,CAAC,MAAkB;QAChC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;IACjC,CAAC;IAKM,WAAW,CAAC,MAAkB;QACnC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACpC,CAAC;IAKM,OAAO,CAAC,MAAkB;QAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,UAAU,IAAI,KAAK,GAAG,UAAU,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,QAAQ,CAAC,MAAkB;QAChC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;IACjC,CAAC;IAKM,SAAS,CAAC,MAAkB;QACjC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IAClC,CAAC;IAKM,UAAU,CAAC,MAAkB;QAClC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;IACnC,CAAC;IAKM,SAAS,CAAC,MAAkB;QACjC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IAClC,CAAC;IAKM,UAAU,CAAC,MAAkB;QAClC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;IACnC,CAAC;IAEO,kBAAkB,CAAC,MAAkB,EAAE,YAAoB;QACjE,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,UAAU,GACd,OAAO,cAAc,KAAK,QAAQ;YAChC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC;gBAC7B,CAAC,CAAC,OAAO;gBACT,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;QAE5B,IAAI,UAAU,KAAK,YAAY,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,wBAAwB,YAAY,SAAS,UAAU,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,MAAkB;QACtC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClD,OAAO,WAAW,IAAI,MAAM,CAAC;QAC/B,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,mBAAmB,CAAC,MAAkB;QAC5C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACxD,OAAO;QACT,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,QAAQ;oBACX,MAAM,YAAY,GAAG,MAA0B,CAAC;oBAChD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;oBACnF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;oBACpD,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC7E,MAAM;gBACR,KAAK,MAAM;oBACT,MAAM,UAAU,GAAG,MAAwB,CAAC;oBAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;oBAC7E,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;oBAChD,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,WAAW,GAAG,MAAyB,CAAC;oBAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;oBAChF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;oBAClD,MAAM;gBACR,KAAK,OAAO;oBACV,IAAI,CAAC,mBAAmB,CAAE,MAA0B,CAAC,KAAK,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,mBAAmB,CAAE,MAAwB,CAAC,MAAM,CAAC,CAAC;oBAC3D,MAAM;YACV,CAAC;QACH,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,IAAY,EAAE,SAAkB;QAClD,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IACnD,CAAC;CACF;AAvQD,8CAuQC"}