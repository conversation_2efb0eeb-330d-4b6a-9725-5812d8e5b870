<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生信息管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e0f7fa 0%, #bbdefb 100%);
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.85);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        
        h1 {
            text-align: center;
            color: #1565c0;
            margin-bottom: 30px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .operate-btn {
            background: linear-gradient(to right, #1976d2, #2196f3);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .operate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
        
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        }
        
        th, td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        th {
            background: linear-gradient(to bottom, #2196f3, #1976d2);
            color: white;
            font-weight: 600;
            font-size: 16px;
        }
        
        tr:nth-child(even) {
            background-color: rgba(225, 245, 254, 0.3);
        }
        
        tr:hover {
            background-color: rgba(179, 229, 252, 0.4);
        }
        
        .action-btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .edit-btn {
            background: linear-gradient(to right, #4caf50, #2e7d32);
            color: white;
        }
        
        .delete-btn {
            background: linear-gradient(to right, #f44336, #d32f2f);
            color: white;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            background: rgba(255, 255, 255, 0.9);
            margin: 10% auto;
            padding: 30px;
            border-radius: 16px;
            width: 500px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        
        .close {
            color: #555;
            float: right;
            font-size: 32px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .close:hover {
            color: #f44336;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: inline-block;
            width: 100px;
            font-weight: 600;
            color: #1565c0;
        }
        
        .form-group input {
            width: 300px;
            padding: 12px;
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
        }
        
        .form-group button {
            background: linear-gradient(to right, #1976d2, #2196f3);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .form-group button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>学生信息管理系统</h1>
        <button class="operate-btn" onclick="openAddModal()">新增学生</button>
        
        <table id="studentTable">
            <thead>
                <tr>
                    <th>学号</th>
                    <th>姓名</th>
                    <th>性别</th>
                    <th>年龄</th>
                    <th>专业</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2021001</td>
                    <td>张三</td>
                    <td>男</td>
                    <td>20</td>
                    <td>计算机科学</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editStudent(this)">修改</button>
                        <button class="action-btn delete-btn" onclick="deleteStudent(this)">删除</button>
                    </td>
                </tr>
                <tr>
                    <td>2021002</td>
                    <td>李四</td>
                    <td>女</td>
                    <td>19</td>
                    <td>软件工程</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editStudent(this)">修改</button>
                        <button class="action-btn delete-btn" onclick="deleteStudent(this)">删除</button>
                    </td>
                </tr>
                <tr>
                    <td>2021003</td>
                    <td>王五</td>
                    <td>男</td>
                    <td>21</td>
                    <td>信息安全</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editStudent(this)">修改</button>
                        <button class="action-btn delete-btn" onclick="deleteStudent(this)">删除</button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 新增/修改学生信息模态框 -->
    <div id="studentModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">新增学生信息</h2>
            <form id="studentForm">
                <div class="form-group">
                    <label>学号：</label>
                    <input type="text" id="studentId" required>
                </div>
                <div class="form-group">
                    <label>姓名：</label>
                    <input type="text" id="studentName" required>
                </div>
                <div class="form-group">
                    <label>性别：</label>
                    <input type="text" id="studentGender" required>
                </div>
                <div class="form-group">
                    <label>年龄：</label>
                    <input type="number" id="studentAge" required>
                </div>
                <div class="form-group">
                    <label>专业：</label>
                    <input type="text" id="studentMajor" required>
                </div>
                <div class="form-group">
                    <button type="submit">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let isEdit = false;
        let editRow = null;

        // 打开新增模态框
        function openAddModal() {
            isEdit = false;
            document.getElementById('modalTitle').textContent = '新增学生信息';
            document.getElementById('studentForm').reset();
            document.getElementById('studentModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('studentModal').style.display = 'none';
        }

        // 编辑学生信息
        function editStudent(btn) {
            isEdit = true;
            editRow = btn.parentNode.parentNode;
            document.getElementById('modalTitle').textContent = '修改学生信息';
            
            // 填充当前行的数据到表单
            document.getElementById('studentId').value = editRow.cells[0].textContent;
            document.getElementById('studentName').value = editRow.cells[1].textContent;
            document.getElementById('studentGender').value = editRow.cells[2].textContent;
            document.getElementById('studentAge').value = editRow.cells[3].textContent;
            document.getElementById('studentMajor').value = editRow.cells[4].textContent;
            
            document.getElementById('studentModal').style.display = 'block';
        }

        // 删除学生信息
        function deleteStudent(btn) {
            if (confirm('确定要删除这条记录吗？')) {
                btn.parentNode.parentNode.remove();
            }
        }

        // 表单提交处理
        document.getElementById('studentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const studentData = {
                id: document.getElementById('studentId').value,
                name: document.getElementById('studentName').value,
                gender: document.getElementById('studentGender').value,
                age: document.getElementById('studentAge').value,
                major: document.getElementById('studentMajor').value
            };
            
            if (isEdit && editRow) {
                // 修改模式：更新表格行
                editRow.cells[0].textContent = studentData.id;
                editRow.cells[1].textContent = studentData.name;
                editRow.cells[2].textContent = studentData.gender;
                editRow.cells[3].textContent = studentData.age;
                editRow.cells[4].textContent = studentData.major;
            } else {
                // 新增模式：添加新行
                const tbody = document.querySelector('#studentTable tbody');
                const newRow = tbody.insertRow();
                newRow.innerHTML = `
                    <td>${studentData.id}</td>
                    <td>${studentData.name}</td>
                    <td>${studentData.gender}</td>
                    <td>${studentData.age}</td>
                    <td>${studentData.major}</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editStudent(this)">修改</button>
                        <button class="action-btn delete-btn" onclick="deleteStudent(this)">删除</button>
                    </td>
                `;
            }
            
            closeModal();
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('studentModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>