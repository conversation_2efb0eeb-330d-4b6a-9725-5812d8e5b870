"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.systemSymbolImport = exports.systemSymbolTable = void 0;
const Import_1 = require("./Import");
exports.systemSymbolTable = [
    '$ion',
    '$ion_1_0',
    '$ion_symbol_table',
    'name',
    'version',
    'imports',
    'symbols',
    'max_id',
    '$ion_shared_symbol_table',
];
exports.systemSymbolImport = new Import_1.Import(null, exports.systemSymbolTable);
//# sourceMappingURL=symbols.js.map