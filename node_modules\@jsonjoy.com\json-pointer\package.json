{"name": "@jsonjoy.com/json-pointer", "packageManager": "yarn@4.5.0", "publishConfig": {"access": "public"}, "version": "1.0.2", "description": "High-performance JSON Pointer implementation", "author": {"name": "<PERSON>ich", "url": "https://github.com/streamich"}, "homepage": "https://github.com/jsonjoy-com/json-pointer", "repository": "jsonjoy-com/json-pointer", "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "keywords": ["json-pointer", "json", "pointer", "jit", "selector", "pick"], "engines": {"node": ">=10.0"}, "main": "lib/index.js", "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "files": ["LICENSE", "lib/"], "license": "Apache-2.0", "scripts": {"format": "biome format ./src", "format:fix": "biome format --write ./src", "lint": "biome lint ./src", "lint:fix": "biome lint --apply ./src", "clean": "npx rimraf@6.0.1 lib typedocs coverage gh-pages yarn-error.log", "build": "tsc --project tsconfig.build.json --module commonjs --target es2020 --outDir lib", "test": "vitest ./src", "coverage": "vitest run --coverage", "typedoc": "npx typedoc@0.25.13 --tsconfig tsconfig.build.json", "build:pages": "npx rimraf@6.0.1 gh-pages && mkdir -p gh-pages && cp -r typedocs/* gh-pages && cp -r coverage gh-pages/coverage", "deploy:pages": "gh-pages -d gh-pages", "publish-coverage-and-typedocs": "yarn typedoc && yarn coverage && yarn build:pages && yarn deploy:pages"}, "peerDependencies": {"tslib": "2"}, "dependencies": {"@jsonjoy.com/codegen": "^1.0.0", "@jsonjoy.com/util": "^1.9.0"}, "devDependencies": {"@biomejs/biome": "^1.9.3", "@types/benchmark": "^2.1.5", "@vitest/coverage-v8": "^2.1.2", "benchmark": "^2.1.4", "config-galore": "^1.0.0", "tslib": "^2.7.0", "typescript": "^5.6.2", "vitest": "^2.1.2"}}