{"version": 3, "file": "AvroSchemaValidator.js", "sourceRoot": "", "sources": ["../../src/avro/AvroSchemaValidator.ts"], "names": [], "mappings": ";;;AAwBA,MAAa,mBAAmB;IAAhC;QACU,iBAAY,GAAG,IAAI,GAAG,EAA2B,CAAC;IAiS5D,CAAC;IA5RQ,cAAc,CAAC,MAAkB;QACtC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAKM,aAAa,CAAC,KAAc,EAAE,MAAkB;QACrD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACxD,CAAC;IAEO,sBAAsB,CAAC,MAAkB;QAC/C,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAE/B,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAE1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,MAAM;oBACT,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAwB,CAAC,CAAC;gBAC3D,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAA2B,CAAC,CAAC;gBACjE,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAuB,CAAC,CAAC;gBACzD,KAAK,MAAM;oBACT,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAwB,CAAC,CAAC;gBAC3D,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAyB,CAAC,CAAC;gBAC7D,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAA0B,CAAC,CAAC;gBAC/D,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAyB,CAAC,CAAC;gBAC7D,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAA0B,CAAC,CAAC;gBACnE,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAA0B,CAAC,CAAC;gBAC/D,KAAK,MAAM;oBACT,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAwB,CAAC,CAAC;gBAC3D,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAyB,CAAC,CAAC;gBAC7D,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAuB,CAAC,CAAC;gBACzD,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAyB,CAAC,CAAC;gBAC7D;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,oBAAoB,CAAC,MAAc;QACzC,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAChG,OAAO,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1E,CAAC;IAEO,mBAAmB,CAAC,MAAuB;QACjD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAElC,KAAK,MAAM,SAAS,IAAI,MAAM,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC;gBAAE,OAAO,KAAK,CAAC;YAG1D,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAAE,OAAO,KAAK,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,MAAsB;QAC/C,OAAO,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;IAChC,CAAC;IAEO,qBAAqB,CAAC,MAAyB;QACrD,OAAO,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC;IACnC,CAAC;IAEO,iBAAiB,CAAC,MAAqB;QAC7C,OAAO,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC;IAC/B,CAAC;IAEO,kBAAkB,CAAC,MAAsB;QAC/C,OAAO,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;IAChC,CAAC;IAEO,mBAAmB,CAAC,MAAuB;QACjD,OAAO,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC;IACjC,CAAC;IAEO,oBAAoB,CAAC,MAAwB;QACnD,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC;IAClC,CAAC;IAEO,mBAAmB,CAAC,MAAuB;QACjD,OAAO,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC;IACjC,CAAC;IAEO,wBAAwB,CAAC,MAAwB;QACvD,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC;IAClC,CAAC;IAEO,oBAAoB,CAAC,MAAwB;QACnD,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;YAAE,OAAO,KAAK,CAAC;QAE5F,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;YAAE,OAAO,KAAK,CAAC;QAClD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAExC,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QACrC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC;YACnD,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAE,OAAO,KAAK,CAAC;YAC7C,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB,CAAC,KAAsB;QAChD,OAAO,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC5G,CAAC;IAEO,kBAAkB,CAAC,MAAsB;QAC/C,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,KAAK,CAAC;QAE3F,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;YAAE,OAAO,KAAK,CAAC;QAClD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAExC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC9C,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;gBAAE,OAAO,KAAK,CAAC;YACtE,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;QAGD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,KAAK,CAAC;QAE3F,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB,CAAC,MAAuB;QACjD,OAAO,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9E,CAAC;IAEO,iBAAiB,CAAC,MAAqB;QAC7C,OAAO,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAEO,mBAAmB,CAAC,MAAuB;QACjD,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAC;QAC7F,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;YAAE,OAAO,KAAK,CAAC;QAElC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;YAAE,OAAO,KAAK,CAAC;QAClD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,0BAA0B,CAAC,KAAc,EAAE,MAAkB;QACnE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAE1B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,MAAM;oBACT,OAAO,KAAK,KAAK,IAAI,CAAC;gBACxB,KAAK,SAAS;oBACZ,OAAO,OAAO,KAAK,KAAK,SAAS,CAAC;gBACpC,KAAK,KAAK;oBACR,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,UAAU,CAAC;gBAC7G,KAAK,MAAM;oBACT,OAAO,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;gBAC7F,KAAK,OAAO,CAAC;gBACb,KAAK,QAAQ;oBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;gBACnC,KAAK,OAAO;oBACV,OAAO,KAAK,YAAY,UAAU,CAAC;gBACrC,KAAK,QAAQ;oBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;gBACnC,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,MAA0B,CAAC,CAAC;gBAC5E,KAAK,MAAM;oBACT,OAAO,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,MAAwB,CAAC,CAAC;gBACxE,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAyB,CAAC,CAAC;gBAC1E,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAuB,CAAC,CAAC;gBACtE,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAyB,CAAC,CAAC;gBAC1E;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,gCAAgC,CAAC,KAAc,EAAE,MAAc;QACrE,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM;gBACT,OAAO,KAAK,KAAK,IAAI,CAAC;YACxB,KAAK,SAAS;gBACZ,OAAO,OAAO,KAAK,KAAK,SAAS,CAAC;YACpC,KAAK,KAAK;gBACR,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,UAAU,CAAC;YAC7G,KAAK,MAAM;gBACT,OAAO,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;YAC7F,KAAK,OAAO,CAAC;YACb,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YACnC,KAAK,OAAO;gBACV,OAAO,KAAK,YAAY,UAAU,CAAC;YACrC,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YACnC;gBAEE,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAClD,OAAO,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrF,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,KAAc,EAAE,MAAwB;QACzE,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI;YAAE,OAAO,KAAK,CAAC;QAC9D,MAAM,GAAG,GAAG,KAAgC,CAAC;QAE7C,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClC,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,UAAU,KAAK,SAAS,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS;gBAAE,OAAO,KAAK,CAAC;YAC1E,IAAI,UAAU,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC;gBAAE,OAAO,KAAK,CAAC;QACzG,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,wBAAwB,CAAC,KAAc,EAAE,MAAsB;QACrE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrE,CAAC;IAEO,yBAAyB,CAAC,KAAc,EAAE,MAAuB;QACvE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QACxC,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACpF,CAAC;IAEO,uBAAuB,CAAC,KAAc,EAAE,MAAqB;QACnE,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI;YAAE,OAAO,KAAK,CAAC;QAC9D,MAAM,GAAG,GAAG,KAAgC,CAAC;QAC7C,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAChG,CAAC;IAEO,yBAAyB,CAAC,KAAc,EAAE,MAAuB;QACvE,OAAO,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC;IACrE,CAAC;IAEO,iBAAiB,CAAC,MAAkB;QAC1C,IAAI,OAAO,MAAM,KAAK,QAAQ;YAAE,OAAO,MAAM,CAAC;QAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QAC1C,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,WAAW,CAAC,IAAY,EAAE,SAAkB;QAClD,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IACnD,CAAC;CACF;AAlSD,kDAkSC"}